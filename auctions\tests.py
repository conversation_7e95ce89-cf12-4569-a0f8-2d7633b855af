from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import Category, AuctionItem, Bid


class AuctionSystemTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create test users
        self.seller = User.objects.create_user(
            username='testseller',
            email='<EMAIL>',
            password='testpass123'
        )

        self.buyer = User.objects.create_user(
            username='testbuyer',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test category
        self.category = Category.objects.create(
            name='Test Electronics',
            description='Test category for electronics'
        )

        # Create test auction
        self.auction = AuctionItem.objects.create(
            title='Test iPhone',
            description='A test iPhone for auction',
            category=self.category,
            seller=self.seller,
            starting_price=Decimal('100.00'),
            current_price=Decimal('100.00'),
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now() + timedelta(days=1),
            status='active'
        )

    def test_home_page_loads(self):
        """Test that home page loads successfully"""
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'AuctionSite')

    def test_auction_detail_page(self):
        """Test auction detail page"""
        response = self.client.get(reverse('auction_detail', kwargs={'pk': self.auction.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.auction.title)

    def test_user_registration(self):
        """Test user registration"""
        response = self.client.post(reverse('register'), {
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful registration
        self.assertTrue(User.objects.filter(username='newuser').exists())

    def test_user_login(self):
        """Test user login"""
        response = self.client.post(reverse('login'), {
            'username': 'testseller',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful login

    def test_place_bid(self):
        """Test placing a bid"""
        self.client.login(username='testbuyer', password='testpass123')

        response = self.client.post(reverse('place_bid', kwargs={'pk': self.auction.pk}), {
            'amount': '150.00'
        })

        self.assertEqual(response.status_code, 302)  # Redirect after successful bid

        # Check if bid was created
        bid = Bid.objects.filter(auction_item=self.auction, bidder=self.buyer).first()
        self.assertIsNotNone(bid)
        self.assertEqual(bid.amount, Decimal('150.00'))

        # Check if auction price was updated
        self.auction.refresh_from_db()
        self.assertEqual(self.auction.current_price, Decimal('150.00'))

    def test_auction_model_properties(self):
        """Test auction model properties"""
        self.assertTrue(self.auction.is_active)
        self.assertIsNotNone(self.auction.time_remaining)
        self.assertEqual(self.auction.bid_count, 0)

    def test_create_auction_requires_login(self):
        """Test that creating auction requires login"""
        response = self.client.get(reverse('create_auction'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_categories_display(self):
        """Test that categories are displayed on home page"""
        response = self.client.get(reverse('home'))
        self.assertContains(response, self.category.name)
